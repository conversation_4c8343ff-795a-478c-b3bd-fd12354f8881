<?php
// +----------------------------------------------------------------------
// | Company: 汕头市海西信息科技有限公司<http://haixee.com/>
// +----------------------------------------------------------------------
// | Author: stormrage<<EMAIL>>
// +----------------------------------------------------------------------
namespace console\controllers;

use common\libraries\guanyierp\GuanYiErp;
use common\models\GuanyierpItems;
use common\models\GuanyierpPurchase;
use common\models\GuanyierpPurchaseArrive;
use common\models\GuanyierpPurchaseArriveDetails;
use common\models\GuanyierpShop;
use common\models\GuanyierpStock;
use common\models\GuanyierpStockOtherInOrder;
use common\models\GuanyierpStockOtherInOrderDetail;
use common\models\GuanyierpTradeDeliverys;
use common\models\GuanyierpTradeDeliverysDetails;
use common\models\GuanyierpTradeDeliverysSku;
use common\models\GuanyierpTradeReturn;
use common\models\product\ProductMonitorTemporary;
use common\models\product\ProductOrderSalesItemShopSum;
use common\models\product\ProductReturnDataRecord;
use common\models\product\ProductSalesDataRecord;
use common\models\product\ProductStockDataRecord;
use common\models\product\ProductTradeDataRecord;
use common\models\product\ProductTradeExpress;
use common\models\product\ProductTradeUser;
use common\models\product\ProductTradeWarehouse;
use common\models\ProductionBatch;
use Yii;
use yii\db\Query;

/**
 * 管易云定时器
 */
class Guanyierp2Controller extends BaseController
{
    /**
     * 管易云erp实例
     * @var \lyhiving\guanyierp\guanyierp
     */
    public $erp;

    /**
     * 数据拉取分页长度
     * @var int
     */
    public $pageSize = 100;

    public function init()
    {
        parent::init();

        $this->erp = (new GuanYiErp())->erp();
    }

    /**
     * 定时入口
     */
    public function actionHour()
    {
        $hour = intval(date('H'));
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        // 5点执行一次
        if ($hour == 5) {
            // 同步采购单
            $this->actionCatchPurchase($yesterday);
            // 同步采购入库单
            $this->actionCatchPurchaseArrive($yesterday);
            // 同步其他入库
            $this->actionCatchStockOtherInOrder($yesterday);
            // 同步产品
            $this->actionCatchItems($yesterday);
            // 同步销量
            // $this->actionCatchTradeDeliverys($yesterday);
            // 同步退货
            $this->actionCatchReturn($yesterday);
        }
        if ($hour == 6) {
            // 更新前15天申请的已审核退货数据
            $this->actionUpdateReturnPastDays();
        }
        // 5点开始，每3小时执行一次
        if ($hour >= 5 && $hour % 2 == 0) {
            // 更新采购单，3小时一次
            $this->actionRefreshPurchase();
            // 更新采购入库单，3小时一次
            $this->actionRefreshPurchaseArrive();
            // 更新库存，3小时一次
            $this->actionRefreshStock();
            // 更新其他入库，3小时一次
            $this->actionRefreshStockOtherInOrder();
            // 更新店铺列表，3小时一次
            $this->actionRefreshShop();
        }
        if ($hour >= 8) {
            $this->actionSku($yesterday);
        }

    }

    /**
     * 生成生产批次表
     * @command php yii guanyierp2/create-production-batch
     */
    public function actionCreateProductionBatch()
    {
        ProductionBatch::generate();
        echo "生产批次表生成完成\n";
    }

    /**
     * 同步发货订单记录和销售记录
     * @link https://support.guanyierp.com/hc/kb/article/1234998/
     * @link https://support.guanyierp.com/hc/kb/article/1234999/
     * @param string $date 日期，默认今天
     * @param int $start_page 开始页码，默认1
     * @param int $end_page 结束页码
     */
    public function actionCatchTradeDeliverys(string $date = '', int $start_page = 1, int $end_page = 9999999)
    {
        try {
            // 默认昨天
            $date = $date ?: date('Y-m-d');
            // 清空当日汇总
            if ($start_page == 1 && $end_page == 9999999) {
                $datetime = strtotime($date);
                ProductTradeDataRecord::deleteAll("date_time >= {$datetime} AND date_time < {$datetime}+86400");
                ProductSalesDataRecord::deleteAll("date_time >= {$datetime} AND date_time < {$datetime}+86400");
            }
            // 最大可插入id
            $id = ProductTradeDataRecord::find()->max('id');
            // 赠品单品列表
            $giveaway_item_list = ProductMonitorTemporary::getGiveawayItemCode();
            // 不同时间需要访问不同接口
            if (time() - strtotime($date) < 7 * 86400) { // 7天之内
                $method = 'gy.erp.trade.deliverys.get';
            } else {
                $method = 'gy.erp.trade.deliverys.history.get';
            }
            // 页码
            $page_no = $start_page;
            while ($page_no <= $end_page) {
                $result = $this->erp->getTo($method, [
                    'start_delivery_date' => "{$date} 00:00:00",
                    'end_delivery_date' => "{$date} 23:59:59",
                    'page_no' => $page_no,
                    'page_size' => 100
                ]);
                if (empty($result['deliverys']))
                    break;

                $trade_insert = array();
                $sales_insert = array();
                foreach ($result['deliverys'] as $delivery) {
                    $id++;
                    // 邮编信息
                    $delivery_statusInfo = $delivery['delivery_statusInfo'];
                    // 发货时间
                    $date_time = strtotime($delivery_statusInfo['delivery_date']);
                    // 平台信息
                    $gs = GuanyierpShop::findOne(['name' => $delivery['shop_name']]);
                    $platform_name = $gs ? $gs->type_name : '';
                    $platform_code = GuanyierpShop::getPlatformTypeByName($platform_name);
                    // 店铺信息
                    $shop_code = $gs ? $gs->code : '';
                    $shop_name = $gs ? $gs->name : '';
                    // 仓库信息
                    $warehouse_code = '';
                    $warehouse_name = '';
                    if ($delivery['warehouse_code']) {
                        $ptw = ProductTradeWarehouse::findOneByWarehouse($delivery['warehouse_code'], $delivery['warehouse_name']);
                        $warehouse_code = $ptw->code;
                        $warehouse_name = $ptw->name;
                    }
                    $express_brand = '';
                    $express_code = '';
                    $express_name = '';
                    if ($delivery['express_code']) {
                        // 快递信息
                        $pte = ProductTradeExpress::findOneByExpress($delivery['express_code'], $delivery['express_name']);
                        $express_brand = $pte->brand;
                        $express_code = $pte->brand;
                        $express_name = $pte->name;
                    }
                    // 商品包信息
                    $details = $delivery['details'];
                    // 会员信息
                    $ptu_id = $delivery['vip_code'] ? ProductTradeUser::findOneByVip($delivery['vip_code'], $platform_code, $platform_name)->id : 0;
                    // 补发订单
                    $is_reissue = strstr($delivery['seller_memo'], '补发') ? 1 : 0;
                    // 统计单品数、sku数、销量、销量（不含赠品）
                    $total_item = [];
                    $total_sku = [];
                    $total_sales = 0;
                    $total_sales_without_giveaway = 0;
                    // 赠品订单标识
                    $is_giveaway_order = 1;
                    foreach ($details as $d) {
                        // sku
                        $sku_code = $d['sku_code'] ?: '';
                        $sku_name = $d['sku_name'] ?: '';
                        // item
                        $item_code = $d['item_code'];
                        // 单品是否赠品
                        $is_giveaway = in_array($item_code, $giveaway_item_list) ? 1 : 0;
                        $is_giveaway_order = $is_giveaway_order == 1 && $is_giveaway == 0 ? 0 : $is_giveaway_order;
                        // 统计
                        if (!isset($total_item[$item_code]))
                            $total_item[$item_code] = $d['item_name'];
                        if (!isset($total_sku[$item_code . $sku_code]))
                            $total_sku[$item_code . $sku_code] = $sku_name;
                        $total_sales += $d['qty'];
                        $total_sales_without_giveaway += $is_giveaway ? $d['qty'] : 0;
                        // 发货记录
                        $sales_insert[] = array_values([
                            'create_time' => strtotime($delivery['create_date']),
                            'trade_id' => $id,
                            'item_code' => $item_code,
                            'item_name' => $d['item_name'] ?: '',
                            'date_time' => $date_time,
                            'date_year' => date('Y', $date_time),
                            'date_month' => date('m', $date_time),
                            'date_day' => date('d', $date_time),
                            'date_week' => date('W', $date_time),
                            'sales' => $d['qty'] ?: 0,
                            'amount' => $d['amount'] ?: 0,
                            'after_amount' => $d['amount_after'] ?: 0,
                            'sku_code' => $sku_code,
                            'sku_name' => $sku_name,
                            'is_giveaway' => $is_giveaway
                        ]);
                    }
                    // 订单记录
                    $trade_insert[] = array_values([
                        'id' => $id,
                        'create_time' => strtotime($delivery['create_date']),
                        'platform_code' => $platform_code,
                        'platform_name' => $platform_name,
                        'shop_code' => $shop_code,
                        'shop_name' => $shop_name,
                        'date_time' => $date_time,
                        'date_year' => date('Y', $date_time),
                        'date_month' => date('m', $date_time),
                        'date_day' => date('d', $date_time),
                        'date_week' => date('W', $date_time),
                        'sales' => $total_sales,
                        'amount' => $delivery['amount'] ?: 0,
                        'warehouse_code' => $warehouse_code,
                        'warehouse_name' => $warehouse_name,
                        'express_brand' => $express_brand,
                        'express_code' => $express_code,
                        'express_name' => $express_name,
                        'standard_weight' => $delivery_statusInfo['standard_weight'] ?: 0,
                        'province' => explode('-', $delivery['area_name'])[0],
                        'vip_code' => $delivery['vip_code'] ?: '',
                        'item_number' => count($total_item),
                        'sku_number' => count($total_sku),
                        'product_trade_user_id' => $ptu_id,
                        'is_reissue' => $is_reissue,
                        'is_giveaway' => $is_giveaway_order,
                        'sales_number_without_giveaway' => $total_sales_without_giveaway
                    ]);
                }

                if (!empty($trade_insert)) {
                    Yii::$app->db
                        ->createCommand()
                        ->batchInsert(
                            ProductTradeDataRecord::tableName(),
                            ['id', 'create_time', 'platform_code', 'platform_name', 'shop_code', 'shop_name', 'date_time', 'date_year', 'date_month', 'date_day', 'date_week', 'sales', 'amount', 'warehouse_code', 'warehouse_name', 'express_brand', 'express_code', 'express_name', 'standard_weight', 'province', 'vip_code', 'item_number', 'sku_number', 'product_trade_user_id', 'is_reissue', 'is_giveaway', 'sales_number_without_giveaway'],
                            $trade_insert
                        )
                        ->execute();

                    Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中
                }
                if (!empty($sales_insert)) {
                    Yii::$app->db
                        ->createCommand()
                        ->batchInsert(
                            ProductSalesDataRecord::tableName(),
                            ['create_time', 'trade_id', 'item_code', 'item_name', 'date_time', 'date_year', 'date_month', 'date_day', 'date_week', 'sales', 'amount', 'after_amount', 'sku_code', 'sku_name', 'is_giveaway'],
                            $sales_insert
                        )
                        ->execute();

                    Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中
                }

                echo "发货更新：第{$page_no}页，插入了" . count($trade_insert) . "条订单记录；" . count($sales_insert) . "条单品记录\n";
                $page_no++;
            }
        } catch (\Throwable $th) {
            echo $th->getMessage() . "\n";
            echo "发货更新出错\n";
        }
    }

    /**
     * 同步采购单列表
     * @param string $date 日期，默认今天
     * @link https://support.guanyierp.com/hc/kb/article/1235021/
     */
    public function actionCatchPurchase(string $date = '')
    {
        try {
            $date = $date ?: date('Y-m-d');

            $insert = [];
            $page_no = 1;
            $hasMore = true;
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpPurchase::METHOD, [
                    'start_create_date' => $date,
                    'end_create_date' => date('Y-m-d', strtotime($date) + 86400),
                    'page_no' => $page_no++,
                    'page_size' => $this->pageSize
                ]);
                if ($result && $result['success'] && $result['total'] > 0) {
                    $hasMore = $result['hasMore'];

                    // 处理数据
                    // 只需要收集编码，数据由更新程序加载
                    foreach ($result['purchaseOrderList'] as $po) {
                        $insert[] = [
                            'code' => $po['code']
                        ];
                    }
                } else {
                    $hasMore = false;
                }
            }

            if (!empty($insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        GuanyierpPurchase::tableName(),
                        ['code'],
                        $insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                echo "采购单：列表{$date}已同步\n";
            } else {
                echo "采购单：{$date}无数据\n";
            }

            // 销毁变量
            unset($insert);
        } catch (\Throwable $th) {
            echo $th->getMessage() . "\n";
            echo "采购单：列表同步出错\n";
        }
    }

    /**
     * 未审核采购单追踪
     * @link http://gop.guanyierp.com/hc/kb/article/1235021/
     */
    public function actionRefreshPurchase()
    {
        try {
            /** @var GuanyierpPurchase[] */
            $list = GuanyierpPurchase::find()
                ->where(['status' => 0])
                ->orderBy('id ASC')
                ->all();
            if (!empty($list)) {
                foreach ($list as $gp) {
                    $gp->setPower($this->erp);
                    $gp->catch();
                }

                $count = count($list);
                echo "采购单：同步了{$count}条\n";
            } else {
                echo "采购单：暂无同步队列\n";
            }
        } catch (\Throwable $th) {
            echo $th->getMessage() . "\n";
            echo "采购单同步出错\n";
        }
    }

    /**
     * 同步采购入库单列表
     * @param string $date 日期，默认今天
     * @link http://gop.guanyierp.com/hc/kb/article/1235038/
     */
    public function actionCatchPurchaseArrive(string $date = '')
    {
        try {
            $date = $date ?: date('Y-m-d');

            $insert = [];
            $page_no = 1;
            $hasMore = true;
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpPurchaseArrive::METHOD, [
                    'start_create' => $date,
                    'end_create' => date('Y-m-d', strtotime($date) + 86400),
                    'page_no' => $page_no++,
                    'page_size' => $this->pageSize
                ]);
                if ($result && $result['success'] && $result['total'] > 0) {
                    $hasMore = $result['hasMore'];

                    // 处理数据
                    // 只需收集编码，数据由更新程序加载
                    foreach ($result['purchaseArrives'] as $pa) {
                        $insert[] = [
                            'code' => $pa['code']
                        ];
                    }
                } else {
                    $hasMore = false;
                }
            }

            if (!empty($insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        GuanyierpPurchaseArrive::tableName(),
                        ['code'],
                        $insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                echo "采购入库单：列表{$date}已同步\n";
            } else {
                echo "采购入库单：{$date}无数据\n";
            }

            // 销毁变量
            unset($insert);
        } catch (\Throwable $th) {
            Yii::error($th->getMessage() . "\n" . $th->getTraceAsString());
            echo $th->getMessage() . "\n";
            echo "采购入库单：列表同步出错\n";
        }
    }

    /**
     * 未审核采购入库单追踪
     * @link http://gop.guanyierp.com/hc/kb/article/1235038/
     */
    public function actionRefreshPurchaseArrive()
    {
        try {
            /** @var GuanyierpPurchaseArrive[] */
            $list = GuanyierpPurchaseArrive::find()
                ->where(['status' => 0])
                ->orderBy('id ASC')
                ->all();
            if (!empty($list)) {
                foreach ($list as $gpa) {
                    $gpa->setPower($this->erp);
                    $gpa->catch();
                }

                $count = count($list);
                echo "采购入库单：同步了{$count}条\n";
            } else {
                echo "采购入库单：暂无同步队列\n";
            }
        } catch (\Throwable $th) {
            Yii::error($th->getMessage() . "\n" . $th->getTraceAsString());
            echo $th->getMessage() . "\n";
            echo "采购入库单同步出错\n";
        }
    }

    /**
     * 商品获取
     * @command php yii guanyierp2/catch-items
     * @link https://support.guanyierp.com/hc/kb/article/1234975/
     */
    public function actionCatchItems()
    {
        try {
            $page = 1;
            $hasMore = true;
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpItems::METHOD, [
                    'page_no' => $page,
                    'page_size' => $this->pageSize
                ]);
                $hasMore = $result['total'] > $page * $this->pageSize;

                if (!empty($result['items'])) {
                    foreach ($result['items'] as $item) {
                        // 商品删除、组合商品事件、排除分类
                        if ($item['combine'] || $item['del'] || in_array($item['category_name'], ProductMonitorTemporary::GUANYI_ITEM_EXCLUDE_CATEGORY_NAME_LIST)) {
                            if ($pmt = ProductMonitorTemporary::findOne(['item_code' => $item['code']])) {
                                $pmt->delete();

                                echo "删除{$item['name']}\n";
                            }
                        } else {
                            // 更新商品
                            if ($pmt = ProductMonitorTemporary::findOne(['item_code' => $item['code']])) {
                                $pmt->item_name = $item['name'];
                                $pmt->category_name = $item['category_name'] ?: '';
                                $pmt->publish_date = $item['create_date'];
                                $pmt->is_giveaway = strstr($pmt->category_name, "赠品") ? 1 : 0;
                                $pmt->cost_price = $item['cost_price'];
                                $pmt->save(false);
                            } else {
                                $pmt = new ProductMonitorTemporary();
                                $pmt->create([
                                    'item_code' => $item['code'],
                                    'item_name' => $item['name'],
                                    'publish_date' => $item['create_date'],
                                    'category_name' => $item['category_name'] ?: '',
                                    'is_giveaway' => strstr($pmt->category_name, "赠品") ? 1 : 0,
                                    'cost_price' => $item['cost_price']
                                ]);
                                echo "{$item['name']}新增成功\n";
                            }
                        }
                    }
                }
                $page++;
            }
            echo "商品同步完毕\n";
        } catch (\Throwable $th) {
            echo $th->getMessage() . "\n";
            echo "商品同步出错\n";
        }
    }

    /**
     * 更新库存
     * @command php yii guanyierp2/refresh-stock
     * @link https://gop.guanyierp.com/hc/kb/article/1235063/
     */
    public function actionRefreshStock()
    {
        try {
            $date = date('Y-m-d', time());
            $datetime = strtotime($date);

            $page = 1;
            $hasMore = true;
            $insert = array();
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpStock::METHOD, [
                    'page_no' => $page,
                    'page_size' => $this->pageSize
                ]);
                $hasMore = $result['total'] > $page * $this->pageSize;

                if (!empty($result['stocks'])) {
                    foreach ($result['stocks'] as $stock) {
                        if (!$stock['del']) {
                            $insert[] = array_values([
                                'create_time' => time(),
                                'update_time' => time(),
                                'delete_time' => 0,
                                'item_code' => $stock['item_code'],
                                'item_name' => $stock['item_name'],
                                'date_time' => time(),
                                'date_year' => date('Y', time()),
                                'date_month' => date('m', time()),
                                'date_day' => date('d', time()),
                                'date_week' => date('W', time()),
                                'qty' => $stock['qty'],
                                'salable_qty' => $stock['salable_qty'],
                                'road_qty' => $stock['road_qty'],
                                'pick_qty' => $stock['pick_qty'],
                                'warehouse_code' => $stock['warehouse_code'],
                                'warehouse_name' => $stock['warehouse_name'],
                            ]);
                        }
                    }
                }

                $page++;
            }

            if (!empty($insert)) {
                // 删除今天的记录
                ProductStockDataRecord::deleteAll("date_time >= {$datetime} AND date_time < {$datetime}+86400");

                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        ProductStockDataRecord::tableName(),
                        ['create_time', 'update_time', 'delete_time', 'item_code', 'item_name', 'date_time', 'date_year', 'date_month', 'date_day', 'date_week', 'qty', 'salable_qty', 'road_qty', 'pick_qty', 'warehouse_code', 'warehouse_name'],
                        $insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                echo "产品库存{$date}已更新\n";
            }

            // 库存告警检查
            ProductMonitorTemporary::warningCheck();
        } catch (\Throwable $th) {
            echo $th->getMessage() . "\n";
            echo "产品库存更新出错\n";
        }
    }

    /**
     * 其他入库单同步
     * @command php yii guanyierp2/catch-stock-other-in-order-all
     */
    public function actionCatchStockOtherInOrderAll()
    {
        $start = strtotime('2024-01-01');
        // today
        $end = strtotime(date('Y-m-d'));

        while ($start < $end) {
            echo date('Y-m-d', $start) . "\n";
            $this->actionCatchStockOtherInOrder(date('Y-m-d', $start));
            $start = strtotime('+1 day', $start);
        }
    }

    /**
     * 其他入库单主表查询接口
     * yii guanyierp2/catch-stock-other-in-order
     * @link https://vip.kingdee.com/article/310008333925022976?productLineId=3&isKnowledge=2&lang=zh-CN
     * @param string $date 创建时间日期，默认今天
     */
    public function actionCatchStockOtherInOrder(string $date = '')
    {
        try {
            // 默认昨天
            $date = $date ?: date('Y-m-d');
            // 删除记录
            $datetime = strtotime($date);
            GuanyierpStockOtherInOrder::deleteAll("create_time >= {$datetime} AND create_time < {$datetime}+86400");

            $page = 1;
            $hasMore = true;
            $insert = array();
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpStockOtherInOrder::METHOD, [
                    'start_date' => "{$date} 00:00:00",
                    'end_date' => "{$date} 23:59:59",
                    'date_type' => 1, // 创建时间
                    'page_no' => $page,
                    'page_size' => $this->pageSize
                ]);
                $hasMore = $result['total'] > $page * $this->pageSize;

                if (!empty($result['order_list'])) {
                    foreach ($result['order_list'] as $list) {
                        $insert[] = array_values([
                            'create_time' => strtotime($list['create_date']),
                            'operate_date' => strtotime($list['operate_date']),
                            'update_time' => time(),
                            'origin_data' => json_encode($list),
                            'code' => $list['code'],
                            'status' => 0,
                            'details' => ''
                        ]);
                    }
                }

                echo "其他入库：第{$page}页读取完毕\n";
                $page++;
            }

            if (!empty($insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        GuanyierpStockOtherInOrder::tableName(),
                        ['create_time', 'operate_date', 'update_time', 'origin_data', 'code', 'status', 'details'],
                        $insert
                    )
                    ->execute();
                foreach ($insert as $row) {
                    $code = $row[4];
                    $order = GuanyierpStockOtherInOrder::find()->where(['code' => $code])->one();
                    echo "$code\n";
                    if ($order) {
                        $order->setPower($this->erp);
                        $order->catch();
                    }
                }
                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中
            }

            echo "其他入库{$date}已更新\n";
        } catch (\Throwable $th) {
            Yii::error($th->getMessage() . "\n" . $th->getTraceAsString());
            echo $th->getMessage() . "\n";
            echo "其他入库更新出错\n";
        }
    }

    /**
     * 未入库其他入库单追踪、获取详情
     * php yii guanyierp2/refresh-stock-other-in-order
     * @link http://gop.guanyierp.com/hc/kb/article/1235038/
     */
    public function actionRefreshStockOtherInOrder()
    {
        try {
            $list = GuanyierpStockOtherInOrder::find()
                ->where(['status' => 0])
                ->orderBy('id ASC')
                ->all();
            if (!empty($list)) {
                foreach ($list as $gsoio) {
                    $gsoio->setPower($this->erp);
                    $gsoio->catch();
                }

                $count = count($list);
                echo "其他入库单：同步了{$count}条\n";
            } else {
                echo "其他入库单：暂无同步队列\n";
            }
        } catch (\Throwable $th) {
            Yii::error($th->getMessage() . "\n" . $th->getTraceAsString());
            echo $th->getMessage() . "\n";
            echo "其他入库单同步出错\n";
        }
    }

    /**
     * 同步已审核的退货单
     * @link https://vip.kingdee.com/article/309018109736812800?productLineId=3&isKnowledge=2&lang=zh-CN
     * @param string $date 日期，默认今天
     */
    public function actionCatchReturn(string $date = '')
    {
        try {
            $date = $date ?: date('Y-m-d', strtotime('-1 day')); // 默认昨天

            $page = 1;
            $hasMore = true;
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpTradeReturn::METHOD, [
                    'start_create' => "{$date} 00:00:00",
                    'end_create' => "{$date} 23:59:59",
                    'cancel' => 0,// 未作废
                    'page_no' => $page,
                    'page_size' => $this->pageSize
                ]);
                $hasMore = $result['total'] > $page * $this->pageSize;
                // 处理数据
                $this->returnData($result);

                echo "{$date}退货：第{$page}页同步完毕\n";
                $page++;
            }
        } catch (\Throwable $th) {
            echo $th->getFile() . "\n" . $th->getLine() . "\n" . $th->getMessage() . "\n";
            echo "{$date}退货同步出错\n";
        }
    }

    private function returnData($result, $past = false)
    {
        if (!empty($result['tradeReturns'])) {
            $gtr_insert = $psdr_insert = $posis_insert = [];
            foreach ($result['tradeReturns'] as $tr) {
                // 过滤：订单已存在或未审核
                if (GuanyierpTradeReturn::findOne(['platform_code' => $tr['platform_code']]) || !$tr['approve']) {
                    continue;
                }

                // 发货单的单品物流成本
                $cost = [];
                $pay_time = 0;
                $delivery = GuanyierpTradeDeliverysDetails::find()->alias('dd')
                    ->select('d.pay_time,dd.item_code,dd.post_cost')
                    ->leftJoin('guanyierp_trade_deliverys d', 'dd.pid=d.id')
                    ->where(['dd.platform_code' => $tr['platform_code']])->asArray()->all();
                if ($delivery) {
                    foreach ($delivery as $v) {
                        $cost[$v['item_code']] = $v['post_cost'];
                        $pay_time = strtotime($v['pay_time']);
                    }
                }
                // 平台信息
                $gs = GuanyierpShop::findOne(['code' => $tr['shop_code']]);
                $platform_name = $gs ? $gs->type_name : '';
                $platform_code = GuanyierpShop::getPlatformTypeByName($platform_name);
                // 店铺信息
                $shop_code = $gs ? $gs->code : $tr['shop_code'];
                $shop_name = $gs ? $gs->name : $tr['shop_name'];
                // 创建时间
                $create_time = strtotime($tr['create_date']);

                $gtr_insert[] = [
                    'code' => $tr['code'],
                    'reason' => $tr['reason'],
                    'note' => $tr['note'],
                    'create_date' => $tr['create_date'],
                    'create_time' => $create_time,
                    'approve_date' => $tr['approve_date'],
                    'approve' => $tr['approve'],
                    'receive_date' => $tr['receive_date'],
                    'shop_name' => $tr['shop_name'],
                    'shop_code' => $tr['shop_code'],
                    'platform_code' => $tr['platform_code'],
                    'platform_refund_id' => $tr['platform_refund_id'],
                    'sanwu_package' => $tr['sanwu_package'],
                    'vip_code' => $tr['vip_code'],
                    'warehousein_code' => $tr['warehousein_code'],
                    'warehouseout_code' => $tr['warehouseout_code'],
                    'express_code' => $tr['express_code'],
                    'express_name' => $tr['express_name'],
                    'express_num' => $tr['express_num'],
                    'receiver_phone' => $tr['receiver_phone'],
                    'details' => json_encode($tr['details']),
                    'payments' => json_encode($tr['payments']),
                    'refund_phase' => $tr['refund_phase'],
                    'refund_type' => $tr['refund_type'],
                    'return_type' => $tr['return_type'],
                    'receive' => $tr['receive'],
                    'agree_refuse' => $tr['agree_refuse'],
                    'order_code' => $tr['order_code'],
                    'modify_date' => $tr['modify_date'],
                    'business_man' => $tr['business_man'],
                    'receiver_name' => $tr['receiver_name'],
                    'receiver_mobile' => $tr['receiver_mobile'],
                    'receiver_zip' => $tr['receiver_zip'],
                    'receiver_address' => $tr['receiver_address'],
                    'area_name' => $tr['area_name'],
                ];

                foreach ($tr['details'] as $detail) {
                    if ($detail['item_code']) {
                        $psdr_insert[] = [
                            'create_time' => $create_time,// 退款申请时间
                            'update_time' => time(),
                            'delete_time' => 0,
                            'item_code' => $detail['item_code'],
                            'item_name' => $detail['item_name'],
                            'shop_code' => $shop_code,
                            'platform_code' => $platform_code,
                            'platform_name' => $platform_name,
                            'shop_name' => $shop_name,
                            'date_time' => $create_time,
                            'date_year' => date('Y', $create_time),
                            'date_month' => date('m', $create_time),
                            'date_day' => date('d', $create_time),
                            'date_week' => date('W', $create_time),
                            'qty' => $detail['qty'],
                            'amount' => $detail['amount_after'], // 让利后金额amount_after，不是实际退款金额amount
                            'platform_order_code' => $tr['platform_code'],
                            'total_cost_price' => $detail['total_cost_price'], // 商品成本
                            'post_cost' => key_exists($detail['item_code'], $cost) ? $cost[$detail['item_code']] : 0, // 物流成本
                        ];
                        if ($past && $pay_time > 0) {
                            $posis_insert[] = [
                                'shop_code' => $shop_code,
                                'shop_name' => $shop_name,
                                'item_code' => $detail['item_code'],
                                'item_name' => $detail['item_name'],
                                'refund_sales_number' => $detail['qty'],
                                'refund_amount' => $detail['amount_after'],
                                'refund_order_number' => 1,
                                'platform_code' => $platform_code,
                                'platform_name' => $platform_name,
                                'create_time' => $create_time,// 退款申请时间
                                'update_time' => time(),
                                'date_time' => $pay_time,// 支付时间
                                'date_year' => date('Y', $pay_time),
                                'date_month' => date('m', $pay_time),
                                'date_day' => date('d', $pay_time),
                                'refund_total_cost_price' => $detail['total_cost_price'], // 商品成本
                                'refund_post_cost' => key_exists($detail['item_code'], $cost) ? $cost[$detail['item_code']] : 0, // 物流成本
                            ];

                        }
                    }

                }
            }
            if (!empty($gtr_insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        GuanyierpTradeReturn::tableName(),
                        ['code', 'reason', 'note', 'create_date', 'create_time', 'approve_date', 'approve', 'receive_date', 'shop_name', 'shop_code', 'platform_code', 'platform_refund_id', 'sanwu_package', 'vip_code', 'warehousein_code', 'warehouseout_code', 'express_code', 'express_name', 'express_num', 'receiver_phone', 'details', 'payments', 'refund_phase', 'refund_type', 'return_type', 'receive', 'agree_refuse', 'order_code', 'modify_date', 'business_man', 'receiver_name', 'receiver_mobile', 'receiver_zip', 'receiver_address', 'area_name'],
                        $gtr_insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                // 销毁变量
                unset($gtr_insert);
            }
            if (!empty($psdr_insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        ProductReturnDataRecord::tableName(),
                        ['create_time', 'update_time', 'delete_time', 'item_code', 'item_name', 'shop_code', 'platform_code', 'platform_name', 'shop_name', 'date_time', 'date_year', 'date_month', 'date_day', 'date_week', 'qty', 'amount', 'platform_order_code', 'total_cost_price', 'post_cost'],
                        $psdr_insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                // 销毁变量
                unset($psdr_insert);
            }
            if (!empty($posis_insert)) {
                Yii::$app->db
                    ->createCommand()
                    ->batchInsert(
                        ProductOrderSalesItemShopSum::tableName(),
                        ['shop_code', 'shop_name', 'item_code', 'item_name', 'refund_sales_number', 'refund_amount', 'refund_order_number', 'platform_code', 'platform_name', 'create_time', 'update_time', 'date_time', 'date_year', 'date_month', 'date_day', 'refund_total_cost_price', 'refund_post_cost'],
                        $posis_insert
                    )
                    ->execute();

                Yii::getLogger()->flush(true); // 参数传 true 表示每次都会将 message 清理到磁盘中

                // 销毁变量
                unset($posis_insert);
            }
        }
    }

    // actionCatchReturn已经更新了昨天的，以便后续更新其他关联的数据
    // 更新前15天到前天的退款数据，因为审核的退款数据可能是前几天申请的
    function actionUpdateReturnPastDays($days = 20)
    {
        try {
            $edate = date('Y-m-d', strtotime("-2 day"));
            $date = date('Y-m-d', strtotime("-{$days} day"));

            $page = 1;
            $hasMore = true;
            while ($hasMore) {
                $result = $this->erp->getTo(GuanyierpTradeReturn::METHOD, [
                    'start_create' => "{$date} 00:00:00",
                    'end_create' => "{$edate} 23:59:59",
                    'cancel' => 0,// 未作废
                    'page_no' => $page,
                    'page_size' => $this->pageSize
                ]);
                $hasMore = $result['total'] > $page * $this->pageSize;

                // 处理数据，昨天之前的数据需要加汇总的产品退货表数据
                $this->returnData($result, true);

                echo "从{$date}到{$edate}的退货：第{$page}页同步完毕\n";
                $page++;
            }
        } catch (\Throwable $th) {
            echo $th->getFile() . "\n" . $th->getLine() . "\n" . $th->getMessage() . "\n";
            echo "{$date}退货同步出错\n";
        }
    }

    /*
     * 管易发货单数据表处理出OA增量数据
     * php yii guanyierp2/sku 2025-07-22 1
     */
    public function actionSku($yesterday = '', $force = 0)
    {
        $stime = $yesterday ?: date('Y-m-d', strtotime('-1 day'));
        if (GuanyierpTradeDeliverysSku::find()->where(['date' => $stime])->count() > 0 && $force == 0) {
            echo "{$stime}已处理\n";
            exit();
        }
        if ($force == 1) {
            // 删除特定日期的数据
            GuanyierpTradeDeliverysSku::deleteAll(['date' => $stime]);
            echo "删除{$stime}数据成功\n";
        }
        $etime = date('Y-m-d', strtotime($stime) + 86400);

        // 按当天发货单数据的支付时间来存储date字段
        $dates = GuanyierpTradeDeliverys::find()->alias('td')
            ->select(['DISTINCT LEFT(td.pay_time,10) as `dates`'])
            ->leftJoin('guanyierp_trade_deliverys_details d', 'd.pid=td.id')
            ->where(['>=', 'td.delivery_date', $stime])
            ->andWhere(['<', 'td.delivery_date', $etime])
            ->andWhere(['!=', 'd.combine_item_code_split', ''])
            ->groupBy('d.platform_goods_id,d.combine_item_code_split');
        $dates = $dates->asArray()->column();

        // 一次性查询所有数据，避免循环查询
        $all_list = GuanyierpTradeDeliverys::find()->alias('td')
            ->select("td.mallId, td.shop_code, mallPlatform as platform, COUNT(DISTINCT td.id) as order_num, platform_item_name, d.platform_goods_id as goods_id, d.combine_item_code_split as sku_code, d.combine_item_name_split as sku_name, sum(d.amount_after) as amount_after, sum(d.post_cost) as post_cost, sum(d.total_cost_price) as product_cost, sum(d.qty) as qty, LEFT(td.pay_time,10) as pay_date")
            ->leftJoin('guanyierp_trade_deliverys_details d', 'd.pid=td.id')
            ->where(['>=', 'td.delivery_date', $stime])
            ->andWhere(['<', 'td.delivery_date', $etime])
            ->andWhere(['!=', 'd.combine_item_code_split', ''])
            ->groupBy('d.platform_goods_id, d.combine_item_code_split')
            ->orderBy('d.combine_item_code_split desc');

        $all_list = $all_list->asArray()->all();
        echo "查询到的记录数: " . count($all_list) . "\n";

        $result = [];
        foreach ($dates as $v) {
            if ($v == '')
                continue;

            foreach ($all_list as $val) {
                // 只处理匹配当前支付时间的记录
                if ($val['pay_date'] != $v) {
                    continue;
                }

                $val['post_cost'] = bcdiv($val['post_cost'], 1, 2);
                if ($val['platform_item_name']) {
                    preg_match('/\|\|(.+?)\[/', $val['platform_item_name'], $matches);
                    $val['goods_name'] = $matches ? $matches[1] : '';
                }
                unset($val['platform_item_name']);
                $val['date'] = $v;
                $val['op_time'] = date('Y-m-d H:i:s', strtotime($v));
                unset($val['pay_date']);

                $key = $v . '_' . $val['goods_id'] . '_' . $val['sku_code'];
                $result[$key] = $val;
            }
        }

        // 退款记录。没有对应sku只有产品的不统计，amount_after金额为0 不统计
        // 退款数据按支付时间分组，与销售数据保持一致
        $return_list = GuanyierpTradeReturn::find()->alias('r')
            ->select('td.mallId, td.shop_code, mallPlatform as platform, platform_item_name, d.platform_goods_id as goods_id, d.combine_item_code_split as sku_code, d.combine_item_name_split as sku_name, COUNT(DISTINCT td.id) as refund_num, sum(d.amount_after) as refund_amount, sum(d.qty) as refund_qty, LEFT(td.pay_time,10) as pay_date')
            ->innerJoin('guanyierp_trade_deliverys td', 'r.platform_code=td.platform_code')
            ->leftJoin('guanyierp_trade_deliverys_details d', 'd.pid=td.id')
            ->where(['>=', 'r.create_time', strtotime($stime)])
            ->andWhere(['<', 'r.create_time', strtotime($etime)])
            ->andWhere(['>', 'd.amount_after', 0])
            ->andWhere(['!=', 'd.combine_item_code_split', ''])
            ->andWhere(['!=', 'td.pay_time', ''])
            ->groupBy('d.platform_goods_id, d.combine_item_code_split, LEFT(td.pay_time,10)')
            ->orderBy('td.mallId asc');
        //            echo $return_list->createCommand()->getRawSql();die;
        $return_list = $return_list->asArray()->all();

        // 整理$result - 合并退款数据
        foreach ($return_list as $row) {
            // 跳过支付时间为空的记录
            if (empty($row['pay_date'])) {
                continue;
            }

            // 使用支付时间作为key，与销售数据保持一致
            $key = $row['pay_date'] . '_' . $row['goods_id'] . '_' . $row['sku_code'];

            // 有退款没销售的情况
            if (!array_key_exists($key, $result)) {
                if ($row['platform_item_name']) {
                    preg_match('/\|\|(.+?)\[/', $row['platform_item_name'], $matches);
                    $row['goods_name'] = $matches ? $matches[1] : '';
                }
                if ($row['platform_item_name']) {
                    preg_match('/\[(\d+)\]/', $row['platform_item_name'], $matches);
                    $row['goods_id'] = $matches ? $matches[1] : '';
                }
                unset($row['platform_item_name']);
                $row['date'] = $row['pay_date'];
                $row['op_time'] = date('Y-m-d H:i:s', strtotime($row['pay_date']));
                unset($row['pay_date']); // 移除临时字段
                $result[$key] = $row;
            } else {
                // 当天sku有销售有退款
                $result[$key]['refund_qty'] = $row['refund_qty'];
                $result[$key]['refund_num'] = $row['refund_num'];
                $result[$key]['refund_amount'] = $row['refund_amount'];
            }
        }
        var_dump($result);
        foreach ($result as $vv) {
            $vv['date'] = $vv['date'] ?? $stime;
            $vv['op_time'] = date('Y-m-d H:i:s');
            $vv['amount_after'] = $vv['amount_after'] ?? 0;
            $vv['post_cost'] = $vv['post_cost'] ?? 0;
            $vv['product_cost'] = $vv['product_cost'] ?? 0;
            $vv['refund_qty'] = $vv['refund_qty'] ?? 0;
            $vv['refund_num'] = $vv['refund_num'] ?? 0;
            $vv['refund_amount'] = $vv['refund_amount'] ?? 0;
            $gytdDetail = new GuanyierpTradeDeliverysSku();
            $gytdDetail->attributes = $vv;
            try {
                $gytdDetail->save();
            } catch (\Exception $ex) {
                Yii::error($ex);
                Yii::error('sku的增量数据：');
                Yii::error($vv);
            }
        }

        echo 'finish!';
        exit;
    }

    public function actionTest()
    {
        return;
        $transaction = Yii::$app->db->beginTransaction();

        $data = GuanyierpStockOtherInOrder::find()->all();
        foreach ($data as $stockOtherInOrder) {
            $origin_data = $stockOtherInOrder->origin_data;
            if ($stockOtherInOrder->operate_date > 10000 || empty($stockOtherInOrder->operate_date)) {
                continue;
            }
            if (empty($origin_data)) {
                // 等于空的跳过
                continue;
            }
            $origin_data = json_decode($origin_data, true);
            if (empty($origin_data['operate_date'])) {
                // 等于空的跳过
                continue;
            }
            $old = $stockOtherInOrder->operate_date;
            $stockOtherInOrder->operate_date = strtotime($origin_data['operate_date']);
            $stockOtherInOrder->save();
            echo "开始更新{$stockOtherInOrder->code}, {$old} => {$stockOtherInOrder->operate_date}\r\n";
        }
        $transaction->commit();
        echo "更新成功";
        // echo date('Y-m-d', 1731906862);
        // echo date('Y-m-d', 1731906863);
        // echo date('Y-m-d', 1732847110);
        // die;
        // 01.002.04.48
        // 其他入库单
        // $all = GuanyierpStockOtherInOrder::find()->andWhere([
        //     '<=',
        //     'create_time',
        //     strtotime('2024-12-03')
        // ])->andWhere([
        //     '>=',
        //     'create_time',
        //     strtotime('2024-09-01')
        // ])->asArray()->all();
        // foreach ($all as $value) {
        //     $result = $this->erp->getTo('gy.erp.stock.other.in.order.detail.get', ['code' => $value['code']]);

        //     if (empty($result['details'])) {
        //         echo "没有找到{$value['code']}\n";
        //         continue;
        //     }

        //     foreach ($result['details'] as $details) {
        //         if (empty($details['item_code'])) {
        //             continue;
        //         }
        //         if (empty($details['qty'])) {
        //             continue;
        //         }
        //         if (empty($details['memo'])) {
        //             continue;
        //         }
        //         $guanyierpStockOtherInOrderDetail = GuanyierpStockOtherInOrderDetail::find()
        //             ->andWhere(['memo' => null])
        //             ->andWhere(['=', 'guanyierp_stock_other_in_order_code', $value['code']])
        //             ->andWhere(['=', 'item_code', $details['item_code']])
        //             ->andWhere(['=', 'qty', $details['qty']])
        //             ->one();

        //         if (empty($guanyierpStockOtherInOrderDetail)) {
        //             continue;
        //         }

        //         $arr = explode('-', $details['memo']);
        //         $memo_oacode = $arr[0];
        //         $guanyierpStockOtherInOrderDetail->memo = $details['memo'];
        //         $guanyierpStockOtherInOrderDetail->memo_oacode = $memo_oacode;
        //         $guanyierpStockOtherInOrderDetail->save();
        //         Yii::info($details['item_code'] . '-' . $details['qty'] . '-' . $value['code'] . '更新成功', 'scheduled_tasks');
        //     }
        // }
        // 其他入库单完
    }

    /**
     * 获取管易店铺列表
     * @command php yii guanyierp2/shop
     */
    public function actionShop()
    {
        $method = "gy.erp.shop.get";
        $page_no = 1;
        $page_size = 100;
        $all_shops = [];
        while (true) {
            $result = $this->erp->getTo($method, [
                'page_no' => $page_no,
                'page_size' => $page_size
            ]);
            if (empty($result['shops'])) {
                break;
            }
            $all_shops = array_merge($all_shops, $result['shops']);
            $page_no++;
        }
        // 使用 MySql ON DUPLICATE 特性
        $sql = "INSERT INTO `guanyierp_shop` (`id`, `nick`, `code`, `name`, `create_date`, `modify_date`, `note`, `type_name`) VALUES ";
        foreach ($all_shops as $shop) {
            $sql .= "('{$shop['id']}', '{$shop['nick']}', '{$shop['code']}', '{$shop['name']}', '{$shop['create_date']}', '{$shop['modify_date']}', '{$shop['note']}', '{$shop['type_name']}'),";
        }
        $sql = rtrim($sql, ',');
        $sql .= " ON DUPLICATE KEY UPDATE `nick` = VALUES(`nick`), `code` = VALUES(`code`), `name` = VALUES(`name`), `create_date` = VALUES(`create_date`), `modify_date` = VALUES(`modify_date`), `note` = VALUES(`note`), `type_name` = VALUES(`type_name`)";
        Yii::$app->db->createCommand($sql)->execute();
        echo "店铺列表同步成功";
    }
}
